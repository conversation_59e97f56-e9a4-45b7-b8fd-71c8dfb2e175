<?php
// Language file for Najvagram Bot
// Persian and English texts

// Persian texts
$welcome_text_fa = "سلام {name} 👋

به ربات نجوا گرام خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید پیام هارا ناشناس ارسال کنید.

📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.";

$btn_anonymous_message_fa = "✨ پیام ناشناس";
$btn_najva_section_fa = "💬 بخش نجوا";
$btn_support_fa = "☎️ پشتیبانی";
$btn_help_fa = "📚 راهنما";
$btn_privacy_fa = "👀 حریم خصوصی";
$btn_language_fa = "🌐 زبان";
$btn_cancel_fa = "❌ لغو";
$btn_main_menu_fa = "🏠 منوی اصلی";

$send_message_text_fa = "📬 ارسال پیام ناشناس

لطفاً یکی از روش‌های زیر را انتخاب کنید:

1️⃣ فوروارد کردن پیام کاربر مورد نظر
2️⃣ ارسال آیدی عددی کاربر
3️⃣ ارسال یوزرنیم کاربر (با @)

پس از ارسال، اطلاعات کاربر نمایش داده شده و می‌توانید پیام ناشناس خود را ارسال کنید.";

$help_text_fa = "📚 راهنما

برای استفاده از ربات به صورت از راه دور شما میتوانید از دو روش اقدام کنید 👇

1. جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Najvagram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس یوزرنیم گیرنده قرار دهید.
مثال :
@Najvagram_Bot سلام @Ali

2.جهت ارسال می بایست از اینلاین استفاده کنید کافی است ابتدا در چت یوزرنیم ربات ( @Najvagram_Bot ) را تایپ کنید و یک فاصله سپس متن و سپس ایدی عددی گیرنده قرار دهید.
مثال :
@Najvagram_Bot سلام USER_ID(آیدی عددی فرد)";

$user_info_error_fa = "❌ خطا در دریافت اطلاعات کاربر

متأسفانه امکان دریافت اطلاعات این کاربر وجود ندارد.

لطفاً از کاربر مورد نظر بخواهید ابتدا ربات را استارت کند سپس مجدداً تلاش نمایید.";

$user_info_text_fa = "اطلاعات کاربر به شرح زیر میباشد :

•• آیدی عددی ~> {user_id}
•• یوزرنیم ~> @{username}
•• اسم ~> {name}";

$user_info_failed_fa = "اوپس 😲

اطلاعات کاربر دریافت نشد !
از او بخواهید ربات را استارت بزند سپس مجدد تلاش کنید ☹️";

$username_info_fa = "✨ اطلاعات کاربر به شرح زیر میباشد :

💬 یوزرنیم وارد شده :
{username}";

$privacy_settings_fa = "👀 تنظیمات حریم‌خصوصی

✱ وضعیت فعلی: {status}

✱ با فعال کردن حریم خصوصی، هیچ کس نمی‌تواند برای شما پیام ناشناس ارسال کند.

⚠️ توجه: در صورت فعال بودن حریم خصوصی، تمام پیام‌های ناشناس ارسالی به شما مسدود خواهد شد.";

$privacy_enabled_fa = "✅ حریم خصوصی فعال شد

از این پس هیچ کس نمی‌تواند برای شما پیام ناشناس ارسال کند.

برای غیرفعال کردن مجدداً به این بخش مراجعه کنید.";

$privacy_disabled_fa = "❌ حریم خصوصی غیرفعال شد

از این پس دیگران می‌توانند برای شما پیام ناشناس ارسال کنند.

برای فعال کردن مجدداً به این بخش مراجعه کنید.";

$support_text_fa = "🤳 پشتیبانی

✱ برای ارتباط با پشتیبانی و گزارش مشکلات می‌توانید از راه‌های زیر اقدام کنید:

🛃 آیدی پشتیبانی: @Sia32vosh

⏰ ساعات پاسخگویی: 9 صبح تا 9 شب

لطفاً مشکل خود را به صورت کامل شرح دهید تا بتوانیم بهترین کمک را به شما ارائه دهیم.";

$inline_send_title_fa = "برای ارسال نجوا اینجا ضربه بزنید ❗️";
$inline_send_desc_fa = "ارسال نجوا (پیام مخفی) به {user}
از @ در متن خود استفاده نکنید !";

$inline_privacy_title_fa = "❌ امکان ارسال نجوا وجود ندارد";
$inline_privacy_desc_fa = "{name} حریم خصوصی خود را فعال کرده است";
$inline_privacy_message_fa = "⚠️ کاربر {name} حریم خصوصی خود را فعال کرده و امکان دریافت پیام ناشناس ندارد.";

$najva_message_fa = "کاربر { {user} } شما یک پیام از طرف ({sender}) دارید !";
$show_message_btn_fa = "🧐 نمایش پیام";
$not_for_you_fa = "کاربر عزیز ! این نجوا برای شما نیست 🤕";

$status_active_fa = "فعال";
$status_inactive_fa = "غیرفعال";
$btn_enable_fa = "✅ فعال کردن";
$btn_disable_fa = "❌ غیرفعال کردن";

// English texts
$welcome_text_en = "Hello {name} 👋

Welcome to Najvagram Bot!

Using this bot, you and your friends can send anonymous messages.

📚 Please read the usage instructions through the help button or /help command.";

$btn_anonymous_message_en = "✨ Anonymous Message";
$btn_najva_section_en = "💬 Najva Section";
$btn_support_en = "☎️ Support";
$btn_help_en = "📚 Help";
$btn_privacy_en = "👀 Privacy";
$btn_language_en = "🌐 Language";
$btn_cancel_en = "❌ Cancel";
$btn_main_menu_en = "🏠 Main Menu";

$send_message_text_en = "📬 Send Anonymous Message

Please choose one of the following methods:

1️⃣ Forward the target user's message
2️⃣ Send the user's numeric ID
3️⃣ Send the user's username (with @)

After sending, the user information will be displayed and you can send your anonymous message.";

$help_text_en = "📚 Help

To use the bot remotely, you can use two methods 👇

1. To send, you must use inline. Just type the bot username (@Najvagram_Bot) in the chat, add a space, then the text, and then the recipient's username.
Example:
@Najvagram_Bot Hello @Ali

2. To send, you must use inline. Just type the bot username (@Najvagram_Bot) in the chat, add a space, then the text, and then the recipient's numeric ID.
Example:
@Najvagram_Bot Hello USER_ID(numeric user ID)";

$user_info_error_en = "❌ Error retrieving user information

Unfortunately, it's not possible to retrieve this user's information.

Please ask the target user to start the bot first, then try again.";

$user_info_text_en = "User information is as follows:

•• Numeric ID ~> {user_id}
•• Username ~> @{username}
•• Name ~> {name}";

$user_info_failed_en = "Oops 😲

User information not received!
Ask them to start the bot and try again ☹️";

$username_info_en = "✨ User information is as follows:

💬 Entered username:
{username}";

$privacy_settings_en = "👀 Privacy Settings

✱ Current status: {status}

✱ By enabling privacy, no one can send you anonymous messages.

⚠️ Note: If privacy is enabled, all anonymous messages sent to you will be blocked.";

$privacy_enabled_en = "✅ Privacy enabled

From now on, no one can send you anonymous messages.

To disable, visit this section again.";

$privacy_disabled_en = "❌ Privacy disabled

From now on, others can send you anonymous messages.

To enable, visit this section again.";

$support_text_en = "🤳 Support

✱ To contact support and report issues, you can use the following methods:

🛃 Support ID: @Sia32vosh

⏰ Response hours: 9 AM to 9 PM

Please describe your problem completely so we can provide the best help.";

$inline_send_title_en = "Tap here to send najva ❗️";
$inline_send_desc_en = "Send najva (secret message) to {user}
Don't use @ in your text!";

$inline_privacy_title_en = "❌ Cannot send najva";
$inline_privacy_desc_en = "{name} has enabled their privacy";
$inline_privacy_message_en = "⚠️ User {name} has enabled their privacy and cannot receive anonymous messages.";

$najva_message_en = "User { {user} } you have a message from ({sender})!";
$show_message_btn_en = "🧐 Show Message";
$not_for_you_en = "Dear user! This najva is not for you 🤕";

$status_active_en = "Active";
$status_inactive_en = "Inactive";
$btn_enable_en = "✅ Enable";
$btn_disable_en = "❌ Disable";

?>
